import 'package:flutter/material.dart';
import 'department_tree.dart';

/// 部门树搜索功能使用示例
class DepartmentTreeSearchExample extends StatefulWidget {
  const DepartmentTreeSearchExample({super.key});

  @override
  State<DepartmentTreeSearchExample> createState() => _DepartmentTreeSearchExampleState();
}

class _DepartmentTreeSearchExampleState extends State<DepartmentTreeSearchExample> {
  final TextEditingController _searchController = TextEditingController();
  final GlobalKey<DepartmentTreeState> _treeKey = GlobalKey<DepartmentTreeState>();
  String? _searchQuery;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String value) {
    setState(() {
      _searchQuery = value.trim().isEmpty ? null : value.trim();
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('部门树搜索示例'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // 搜索栏
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: '搜索部门名称...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon:
                          _searchQuery != null
                              ? IconButton(icon: const Icon(Icons.clear), onPressed: _clearSearch)
                              : null,
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.0)),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                    ),
                    onChanged: _onSearchChanged,
                  ),
                ),
                const SizedBox(width: 12),
                // 搜索状态指示器
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                  decoration: BoxDecoration(
                    color:
                        _searchQuery != null
                            ? Colors.green.withValues(alpha: 0.1)
                            : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6.0),
                  ),
                  child: Text(
                    _searchQuery != null ? '搜索中' : '显示全部',
                    style: TextStyle(
                      color: _searchQuery != null ? Colors.green : Colors.grey,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 部门树
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: DepartmentTree(
                key: _treeKey,
                searchQuery: _searchQuery,
                showCheckbox: true,
                onNodeTap: (department) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('点击了部门: ${department.departmentName}'),
                      duration: const Duration(seconds: 1),
                    ),
                  );
                },
                onNodeSelected: (department, isSelected) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        '${isSelected ? '选中' : '取消选中'}部门: ${department.departmentName}',
                      ),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                },
              ),
            ),
          ),

          // 操作按钮
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    final checkedDepartments =
                        _treeKey.currentState?.getAllCheckedDepartments() ?? [];
                    showDialog(
                      context: context,
                      builder:
                          (context) => AlertDialog(
                            title: const Text('已选中的部门'),
                            content:
                                checkedDepartments.isEmpty
                                    ? const Text('没有选中任何部门')
                                    : Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children:
                                          checkedDepartments
                                              .map((dept) => Text('• ${dept.departmentName}'))
                                              .toList(),
                                    ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                child: const Text('确定'),
                              ),
                            ],
                          ),
                    );
                  },
                  icon: const Icon(Icons.list),
                  label: const Text('查看选中'),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    _treeKey.currentState?.refresh();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('已刷新部门数据'), duration: Duration(seconds: 2)),
                    );
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('刷新数据'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
