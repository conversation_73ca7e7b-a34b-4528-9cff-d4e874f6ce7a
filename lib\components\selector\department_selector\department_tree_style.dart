import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';

/// 部门树状选择器样式类
class DepartmentTreeStyles {
  /// 主容器样式
  static BoxDecoration containerDecoration(BuildContext context) {
    return BoxDecoration(border: Border(right: BorderSide(color: context.border300, width: 1)));
  }

  /// 列表视图内边距
  static const EdgeInsets listViewPadding = EdgeInsets.all(8.0);

  /// 树节点容器外边距
  static EdgeInsets nodeContainerMargin(int indentLevel) {
    return EdgeInsets.only(
      left: indentLevel * 20.0, // 根据层级缩进
      bottom: 2.0,
    );
  }

  /// 节点材质背景色
  static Color nodeMaterialColor(BuildContext context, bool isSelected) {
    return isSelected ? context.background200 : Colors.transparent;
  }

  /// 节点材质圆角
  static BorderRadius get nodeBorderRadius => BorderRadius.circular(AppRadiusSize.radius4);

  /// 节点悬停颜色
  static Color nodeHoverColor(BuildContext context) {
    return context.activeGrayColor.withValues(alpha: 0.5);
  }

  /// 节点点击水波纹颜色
  static Color nodeSplashColor(BuildContext context) {
    return context.activeGrayColor;
  }

  /// 节点容器内边距
  static const EdgeInsets nodeContainerPadding = EdgeInsets.symmetric(horizontal: 8.0);

  /// 节点固定高度
  static const double nodeHeight = 36.0;

  /// 展开/折叠图标区域尺寸
  static const Size expandIconSize = Size(20, 20);

  /// 展开/折叠图标样式
  static IconData get expandIcon => IconFont.mianxing_xiala;

  /// 展开/折叠图标大小
  static const double expandIconSizeValue = 16;

  /// 展开/折叠图标颜色
  static Color expandIconColor(BuildContext context) {
    return context.icon100;
  }

  /// 展开/折叠动画持续时间
  static const Duration expandAnimationDuration = Duration(milliseconds: 300);

  /// 图标与复选框之间的间距
  static const SizedBox iconCheckboxSpacing = SizedBox(width: 0);

  /// 复选框高度
  static const double checkboxHeight = 20;

  /// 复选框与文本之间的间距
  static const SizedBox checkboxTextSpacing = SizedBox(width: 0);

  /// 部门名称文本样式
  static TextStyle departmentNameTextStyle(BuildContext context, bool isSelected) {
    return TextStyle(
      color: context.textPrimary,
      fontWeight: FontWeight.normal,
      fontSize: 13, // 固定字体大小
    );
  }

  /// 文本溢出处理
  static const TextOverflow textOverflow = TextOverflow.ellipsis;

  /// 文本最大行数
  static const int textMaxLines = 1;

  /// 文本对齐方式
  static const Alignment textAlignment = Alignment.centerLeft;

  /// 搜索高亮背景色
  static Color searchHighlightBackgroundColor(BuildContext context) {
    return Colors.yellow.withValues(alpha: 0.3);
  }

  /// 搜索高亮文本样式
  static TextStyle searchHighlightTextStyle(BuildContext context, bool isSelected) {
    return departmentNameTextStyle(context, isSelected).copyWith(
      backgroundColor: searchHighlightBackgroundColor(context),
      fontWeight: FontWeight.bold,
    );
  }
}
