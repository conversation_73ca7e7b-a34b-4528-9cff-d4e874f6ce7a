# 部门树组件 (DepartmentTree)

一个功能完整的 Flutter 部门树状选择器组件，支持搜索、复选框选择和层级展示。

## 🚀 功能特性

- ✅ **树状层级显示** - 支持多层级部门结构
- ✅ **复选框选择** - 支持单选和多选模式
- ✅ **实时搜索** - 支持部门名称模糊搜索
- ✅ **搜索高亮** - 匹配文本高亮显示
- ✅ **智能展开** - 搜索时自动展开相关路径
- ✅ **响应式设计** - 适配不同屏幕尺寸
- ✅ **自定义样式** - 可自定义外观和交互效果

## 📦 基本用法

### 简单使用

```dart
DepartmentTree(
  onNodeTap: (department) {
    print('点击了部门: ${department.departmentName}');
  },
)
```

### 带复选框的多选模式

```dart
DepartmentTree(
  showCheckbox: true,
  onNodeSelected: (department, isSelected) {
    print('部门 ${department.departmentName} ${isSelected ? '被选中' : '被取消'}');
  },
)
```

### 带搜索功能

```dart
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  String? searchQuery;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TextField(
          decoration: InputDecoration(hintText: '搜索部门...'),
          onChanged: (value) {
            setState(() {
              searchQuery = value.isEmpty ? null : value;
            });
          },
        ),
        Expanded(
          child: DepartmentTree(
            searchQuery: searchQuery,
            showCheckbox: true,
          ),
        ),
      ],
    );
  }
}
```

## 🔧 API 参考

### DepartmentTree 参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `showCheckbox` | `bool` | `false` | 是否显示复选框 |
| `searchQuery` | `String?` | `null` | 搜索查询字符串 |
| `onNodeTap` | `Function(DepartmentModel)?` | `null` | 节点点击回调 |
| `onNodeSelected` | `Function(DepartmentModel, bool)?` | `null` | 节点选择回调 |

### DepartmentTreeState 公开方法

| 方法 | 返回类型 | 描述 |
|------|----------|------|
| `refresh()` | `void` | 刷新部门数据 |
| `getAllCheckedDepartments()` | `List<DepartmentModel>` | 获取所有选中的部门 |
| `setSearchQuery(String?)` | `void` | 手动设置搜索查询 |
| `clearSearch()` | `void` | 清除搜索 |
| `getCurrentSearchQuery()` | `String?` | 获取当前搜索查询 |

## 🎨 搜索功能详解

### 搜索行为

1. **实时搜索** - 搜索参数变化时立即更新显示结果
2. **模糊匹配** - 支持部门名称的部分匹配
3. **不区分大小写** - 搜索时忽略大小写
4. **路径展开** - 自动展开包含匹配节点的父节点路径
5. **节点过滤** - 只显示匹配的节点及其父节点路径

### 视觉效果

- **高亮显示** - 匹配的文本使用黄色背景和粗体显示
- **智能隐藏** - 不匹配的节点完全隐藏
- **保持结构** - 维持树的层级结构和缩进

### 性能优化

- **高效算法** - 使用递归算法进行树遍历和过滤
- **状态管理** - 智能的节点状态管理，避免不必要的重建
- **内存优化** - 合理的数据结构设计，减少内存占用

## 📝 示例代码

完整的使用示例请参考 `department_tree_search_example.dart` 文件。

## 🎯 最佳实践

1. **搜索输入防抖** - 建议在搜索输入时添加防抖逻辑，避免频繁更新
2. **状态管理** - 使用 GlobalKey 来访问组件的公开方法
3. **错误处理** - 在网络请求失败时提供适当的错误提示
4. **用户体验** - 提供搜索状态指示器和清除搜索按钮

## 🔄 更新日志

### v1.1.0 (最新)
- ✅ 新增搜索功能
- ✅ 新增搜索高亮显示
- ✅ 新增公开方法用于外部控制
- ✅ 优化性能和用户体验

### v1.0.0
- ✅ 基础树状显示功能
- ✅ 复选框选择功能
- ✅ 节点展开/折叠功能
